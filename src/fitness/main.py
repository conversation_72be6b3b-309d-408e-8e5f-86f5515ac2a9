import asyncio
import streamlit as st
from .manager import FitnessManager


class StreamlitFitnessPrinter:
    """Streamlit-compatible printer that updates status in the UI"""

    def __init__(self):
        self.status_container = st.empty()
        self.items = {}

    def update_item(self, item_id: str, content: str, is_done: bool = False, hide_checkmark: bool = False):
        self.items[item_id] = (content, is_done, hide_checkmark)
        self._update_display()

    def _update_display(self):
        status_text = []
        for _, (content, is_done, hide_checkmark) in self.items.items():
            if is_done:
                prefix = "✅ " if not hide_checkmark else ""
                status_text.append(f"{prefix}{content}")
            else:
                status_text.append(f"🔄 {content}")

        if status_text:
            self.status_container.markdown("\n".join(status_text))

    def end(self):
        pass  # No cleanup needed for Streamlit


async def run_fitness_planning_streamlit(lifestyle: str, goals: str, timeline: str, results_container):
    """Run fitness planning with Streamlit UI updates"""

    # Create a custom manager that works with Streamlit
    class StreamlitFitnessManager(FitnessManager):
        def __init__(self):
            # Don't call super().__init__() to avoid Rich console
            self.printer = StreamlitFitnessPrinter()

        def _display_final_workout_plan(self, plan):
            """Display the final approved workout plan in Streamlit"""
            with results_container.expander("🏋️ Final Workout Plan", expanded=True):
                st.markdown(f"## {plan.plan_name}")

                st.markdown("### 📋 Overview")
                st.write(plan.overview)

                st.markdown("### 📅 Weekly Schedule")
                for day in plan.weekly_schedule:
                    with st.expander(f"{day.day} - {day.focus} ({day.duration})"):
                        if day.exercises:
                            for exercise in day.exercises:
                                st.write(f"**{exercise.name}**: {exercise.sets} sets x {exercise.reps}")
                                st.write(f"Rest: {exercise.rest_time}")
                                if exercise.instructions:
                                    st.write(f"Instructions: {exercise.instructions}")
                                st.write("---")
                        else:
                            st.write("Rest Day")

                st.markdown("### 🍎 Nutrition Guidelines")
                for nutrition in plan.nutrition_guidelines:
                    st.write(f"**{nutrition.meal_type}**: {nutrition.recommendations}")
                    if nutrition.timing:
                        st.write(f"Timing: {nutrition.timing}")

                st.markdown("### ⚠️ Safety Notes")
                for note in plan.safety_notes:
                    st.write(f"• {note}")

                st.markdown("### 📈 Progression Tips")
                for tip in plan.progression_tips:
                    st.write(f"• {tip}")

                st.markdown(f"### ⏱️ Estimated Timeline")
                st.write(plan.estimated_timeline)

        async def _generate_and_verify_workout_plan_streamlit(self, lifestyle: str, goals: str, timeline: str, search_result, results_container):
            """Generate workout plan and verify it using LLM as a judge pattern - Streamlit version"""

            from agents import Runner, ItemHelpers, TResponseInputItem
            from agents_custom.workout_plan_generator import workout_plan_generator
            from agents_custom.workout_plan_verifier import workout_plan_verifier

            # Prepare input for workout plan generation
            plan_input = f"""
            User Information:
            - Current Lifestyle: {lifestyle}
            - Fitness Goals: {goals}
            - Timeline: {timeline}

            Research Findings:
            {search_result.summary if search_result else "No research data available"}

            Please create a comprehensive workout plan based on this information.
            """

            input_items: list[TResponseInputItem] = [{"content": plan_input, "role": "user"}]
            latest_plan = None
            attempt_count = 0
            max_attempts = 4

            self.printer.update_item("plan_generation", "Generating workout plan...")

            # Create container for plan iterations
            iterations_container = results_container.expander("🔄 Plan Generation & Verification Process", expanded=True)

            # LLM as a judge loop - similar to the OpenAI example
            while attempt_count < max_attempts:
                attempt_count += 1

                try:
                    # Generate workout plan
                    plan_result = await Runner.run(
                        workout_plan_generator,
                        input_items,
                    )

                    latest_plan = plan_result.final_output
                    input_items = plan_result.to_input_list()

                    self.printer.update_item(
                        "plan_generation",
                        f"Generated workout plan (attempt {attempt_count})",
                        is_done=True
                    )

                    # Verify the workout plan
                    self.printer.update_item("plan_verification", f"Verifying workout plan (attempt {attempt_count})...")

                    verification_result = await Runner.run(
                        workout_plan_verifier,
                        input_items,
                    )

                    evaluation = verification_result.final_output

                    self.printer.update_item(
                        "plan_verification",
                        f"Plan evaluation: {evaluation.overall_score}",
                        is_done=True
                    )

                    # Display evaluation in Streamlit
                    with iterations_container.container():
                        st.markdown(f"### Attempt {attempt_count} Evaluation")
                        st.write(f"**Overall Score:** {evaluation.overall_score}")

                        if evaluation.safety_concerns:
                            st.markdown("**Safety Concerns:**")
                            for concern in evaluation.safety_concerns:
                                st.write(f"- {concern.concern} (Severity: {concern.severity})")
                                st.write(f"  Recommendation: {concern.recommendation}")

                        if evaluation.effectiveness_issues:
                            st.markdown("**Effectiveness Issues:**")
                            for issue in evaluation.effectiveness_issues:
                                st.write(f"- {issue.issue} (Impact: {issue.impact})")
                                st.write(f"  Suggestion: {issue.suggestion}")

                        if evaluation.positive_aspects:
                            st.markdown("**Positive Aspects:**")
                            for aspect in evaluation.positive_aspects:
                                st.write(f"- {aspect}")

                        st.write(f"**Specific Feedback:** {evaluation.specific_feedback}")

                    # Check if plan passes
                    if evaluation.overall_score == "pass":
                        st.success("✅ Workout plan approved! Generating final plan...")
                        break
                    elif evaluation.overall_score == "fail" and attempt_count >= max_attempts:
                        st.warning("⚠️ Maximum attempts reached. Using best available plan with noted concerns.")
                        break
                    else:
                        st.info("🔄 Plan needs improvement. Generating revised plan...")

                        # Add feedback to input for next iteration
                        feedback_content = f"""
                        Feedback from fitness expert evaluation:

                        Overall Score: {evaluation.overall_score}

                        Safety Concerns:
                        {chr(10).join([f"- {c.concern}: {c.recommendation}" for c in evaluation.safety_concerns])}

                        Effectiveness Issues:
                        {chr(10).join([f"- {i.issue}: {i.suggestion}" for i in evaluation.effectiveness_issues])}

                        Specific Feedback: {evaluation.specific_feedback}

                        Please revise the workout plan addressing these concerns.
                        """

                        input_items.append({"content": feedback_content, "role": "user"})

                        # Reset for next iteration
                        self.printer.update_item("plan_generation", "Revising workout plan...")

                except Exception as e:
                    st.error(f"Error in attempt {attempt_count}: {str(e)}")
                    if attempt_count >= max_attempts:
                        break
                    continue

            # Display final workout plan
            if latest_plan:
                self._display_final_workout_plan(latest_plan)
            else:
                st.error("❌ Failed to generate a workout plan. Please try again.")

    mgr = StreamlitFitnessManager()

    try:
        # Step 1: Search for fitness information
        mgr.printer.update_item("fitness_search", "Searching for fitness information...")

        from agents import Runner
        from agents_custom.fitness_search_agent import fitness_search_agent

        search_query = f"Create workout plan for {goals} with {lifestyle} lifestyle in {timeline}"
        search_run_result = await Runner.run(
            starting_agent=fitness_search_agent,
            input=f"Search for fitness information to help create a workout plan: {search_query}"
        )
        search_result = search_run_result.final_output

        mgr.printer.update_item(
            "fitness_search",
            f"Found {len(search_result.search_results)} fitness resources",
            is_done=True
        )

        # Display search results in Streamlit
        with results_container.expander("🔍 Fitness Research Results", expanded=True):
            st.write(f"**Search Queries:** {', '.join(search_result.search_queries)}")
            st.write("**Search Results:**")
            for i, result in enumerate(search_result.search_results, 1):
                st.write(f"{i}. **{result.title}**")
                st.write(f"   URL: {result.url}")
                st.write(f"   Snippet: {result.snippet[:150]}...")
                if hasattr(result, 'published_date') and result.published_date:
                    st.write(f"   Published: {result.published_date}")
                st.write("")

            st.write(f"**Research Summary:** {search_result.summary}")

    except Exception as e:
        mgr.printer.update_item(
            "fitness_search",
            f"Error searching for fitness information: {str(e)}",
            is_done=True
        )
        st.error(f"Error in fitness search: {str(e)}")
        search_result = None

    # Step 2: Generate and verify workout plan
    await mgr._generate_and_verify_workout_plan_streamlit(lifestyle, goals, timeline, search_result, results_container)

    mgr.printer.end()


def main():
    st.title("🏋️ Fitness Coach Assistant")
    st.write("Let me ask you a few questions to create your personalized fitness plan!")

    # Initialize session state
    if "fitness_plan_generated" not in st.session_state:
        st.session_state.fitness_plan_generated = False
    if "user_info" not in st.session_state:
        st.session_state.user_info = {}

    # User input form
    with st.form("fitness_questionnaire"):
        st.markdown("### Tell me about yourself:")

        lifestyle = st.selectbox(
            "1. Current Lifestyle & Activity Level:",
            ["", "Sedentary", "Lightly Active", "Moderately Active", "Very Active"],
            help="How active are you currently in your daily life?"
        )

        goals = st.text_input(
            "2. Fitness Goals:",
            placeholder="e.g., lose weight, build muscle, improve endurance, general fitness",
            help="What do you want to achieve with your fitness plan?"
        )

        timeline = st.selectbox(
            "3. Timeline:",
            ["", "3 months", "6 months", "1 year", "Other"],
            help="How long do you want to achieve these goals?"
        )

        if timeline == "Other":
            timeline = st.text_input("Please specify your timeline:")

        submitted = st.form_submit_button("Create My Fitness Plan 🚀")

        if submitted:
            if not lifestyle or not goals or not timeline:
                st.error("Please fill in all fields to create your personalized fitness plan.")
            else:
                st.session_state.user_info = {
                    "lifestyle": lifestyle,
                    "goals": goals,
                    "timeline": timeline
                }
                st.session_state.fitness_plan_generated = True

    # Generate fitness plan if form is submitted
    if st.session_state.fitness_plan_generated and st.session_state.user_info:
        user_info = st.session_state.user_info

        st.markdown("---")
        st.markdown("### Your Information:")
        st.write(f"**Lifestyle:** {user_info['lifestyle']}")
        st.write(f"**Goals:** {user_info['goals']}")
        st.write(f"**Timeline:** {user_info['timeline']}")

        st.markdown("### Creating your personalized fitness plan...")

        # Create containers for status and results
        status_container = st.empty()
        results_container = st.container()

        # Run the fitness planning
        try:
            asyncio.run(run_fitness_planning_streamlit(
                user_info['lifestyle'],
                user_info['goals'],
                user_info['timeline'],
                results_container
            ))
        except Exception as e:
            st.error(f"Sorry, I encountered an error while creating your fitness plan: {str(e)}")

        # Clear status after completion
        status_container.empty()

        # Add button to start over
        if st.button("Create Another Plan"):
            st.session_state.fitness_plan_generated = False
            st.session_state.user_info = {}
            st.rerun()


if __name__ == "__main__":
    main()
