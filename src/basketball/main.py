import asyncio
import streamlit as st
from .manager import BasketballManager


class StreamlitPrinter:
    """Streamlit-compatible printer that updates status in the UI"""

    def __init__(self):
        self.status_container = st.empty()
        self.items = {}

    def update_item(self, item_id: str, content: str, is_done: bool = False):
        self.items[item_id] = (content, is_done)
        self._update_display()

    def _update_display(self):
        status_text = []
        for _, (content, is_done) in self.items.items():
            if is_done:
                status_text.append(f"✅ {content}")
            else:
                status_text.append(f"🔄 {content}")

        if status_text:
            self.status_container.markdown("\n".join(status_text))

    def end(self):
        pass  # No cleanup needed for Streamlit


async def run_basketball_analysis_streamlit(question: str, results_container):
    """Run basketball analysis with Streamlit UI updates"""

    # Create a custom manager that works with Streamlit
    class StreamlitBasketballManager(BasketballManager):
        def __init__(self):
            # Don't call super().__init__() to avoid Rich console
            self.printer = StreamlitPrinter()

    mgr = StreamlitBasketballManager()

    # Capture the results
    search_result = None
    data_analysis = None
    final_answer = ""

    try:
        # Step 1: Generate search queries
        mgr.printer.update_item("search_queries", "Generating search queries...")

        from agents import Runner
        from agents_custom.basketball_search_agent import basketball_search_agent

        search_run_result = await Runner.run(
            starting_agent=basketball_search_agent,
            input=f"Generate 2 search queries and perform web searches to help answer this basketball question: {question}"
        )
        search_result = search_run_result.final_output

        mgr.printer.update_item(
            "search_queries",
            f"Generated {len(search_result.search_queries)} search queries and found {len(search_result.search_results)} results",
            is_done=True
        )

        # Display search results in Streamlit
        with results_container.expander("🔍 Web Search Results", expanded=True):
            st.write(f"**Search Queries:** {', '.join(search_result.search_queries)}")
            st.write("**Search Results:**")
            for i, result in enumerate(search_result.search_results, 1):
                st.write(f"{i}. **{result.title}**")
                st.write(f"   URL: {result.url}")
                st.write(f"   Snippet: {result.snippet}")
                if hasattr(result, 'published_date') and result.published_date:
                    st.write(f"   Published: {result.published_date}")
                st.write("")

            st.write(f"**Summary:** {search_result.summary}")

    except Exception as e:
        mgr.printer.update_item(
            "search_queries",
            f"Error generating search queries: {str(e)}",
            is_done=True
        )
        st.error(f"Error in search queries: {str(e)}")

    try:
        # Step 2: Analyze basketball data
        mgr.printer.update_item("data_analysis", "Analyzing basketball datasets...")

        from agents_custom.basketball_data_agent import analyze_basketball_data

        data_analysis = await analyze_basketball_data(question)

        mgr.printer.update_item(
            "data_analysis",
            f"Analyzed basketball data - found {len(data_analysis.insights)} insights",
            is_done=True
        )

        # Display data analysis results in Streamlit
        with results_container.expander("📊 Basketball Data Analysis", expanded=True):
            if data_analysis.queries:
                st.write("**Generated Queries:**")
                for i, query in enumerate(data_analysis.queries, 1):
                    st.write(f"{i}. {query.query_description}")
                    st.code(query.pandas_code, language="python")

            st.write("**Analysis Results:**")
            for i, insight in enumerate(data_analysis.insights, 1):
                st.write(f"{i}. {insight.insight}")
                st.write(f"   Supporting Data: {insight.supporting_data}")
                st.write(f"   Confidence: {insight.confidence}/10")
                st.write("")

    except Exception as e:
        mgr.printer.update_item(
            "data_analysis",
            f"Error analyzing data: {str(e)}",
            is_done=True
        )
        st.error(f"Error in data analysis: {str(e)}")

    try:
        # Step 3: Generate final answer
        mgr.printer.update_item("final_answer", "Generating comprehensive answer...")

        final_answer = await mgr._generate_final_answer(question, search_result, data_analysis)

        mgr.printer.update_item(
            "final_answer",
            "Answer generated successfully",
            is_done=True
        )

        # Display final answer
        with results_container.expander("🏀 Final Answer", expanded=True):
            st.markdown(final_answer)

    except Exception as e:
        mgr.printer.update_item(
            "final_answer",
            f"Error generating final answer: {str(e)}",
            is_done=True
        )
        st.error(f"Error generating final answer: {str(e)}")

    mgr.printer.end()


def main():
    st.title("🏀 Basketball Question Answering Assistant")
    st.write("Ask me any basketball question and I'll search the web and analyze basketball data to provide you with a comprehensive answer!")

    # Initialize session state
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Chat input
    if prompt := st.chat_input("Ask me a basketball question..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # Generate assistant response
        with st.chat_message("assistant"):
            # Create containers for status and results
            status_container = st.empty()
            results_container = st.container()

            # Run the analysis
            try:
                asyncio.run(run_basketball_analysis_streamlit(prompt, results_container))
                response = f"I've analyzed your question: '{prompt}' using web search and basketball data analysis. Check the detailed results above!"
            except Exception as e:
                response = f"Sorry, I encountered an error while processing your question: {str(e)}"
                st.error(response)

            # Clear status after completion
            status_container.empty()

        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})


if __name__ == "__main__":
    main()
