from pydantic import BaseModel
from typing import List

from agents import Agent

# A sub‑agent focused on analyzing companies and matching them to user preferences
WEB_PROMPT = (
    "You are a web agent. You analyze companies based on user preferences and determine "
    "which companies best match what the user is looking for. You consider factors like "
    "company size, industry, culture, and growth potential."
)


class CompanyMatch(BaseModel):
    company_name: str
    match_score: int  # 1-10 scale
    reasoning: str
    """Why this company matches the user's preferences"""


class WebSummary(BaseModel):
    matches: List[CompanyMatch]
    """List of companies that match user preferences with reasoning."""


web_agent = Agent(
    name="WebAgent",
    instructions=WEB_PROMPT,
    output_type=WebSummary,
)