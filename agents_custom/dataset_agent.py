import requests
import re
from pydantic import BaseModel
from typing import List

from agents import Agent

# A sub‑agent focused on fetching job data from GitHub repositories
DATASET_PROMPT = (
    "You are a dataset agent. You fetch job listings from GitHub repositories and return "
    "the top 5 most recent job postings. You parse the markdown content and extract "
    "company names, roles, and locations."
)


class JobListing(BaseModel):
    company: str
    role: str
    location: str
    application_url: str


class DatasetSummary(BaseModel):
    jobs: List[JobListing]
    """List of top 5 most recent job listings."""


async def fetch_jobs(job_type: str) -> List[JobListing]:
    """Fetch job listings from the appropriate GitHub URL"""
    if job_type == "intern":
        url = "https://raw.githubusercontent.com/SimplifyJobs/Summer2026-Internships/refs/heads/dev/README.md"
    else:  # new_grad
        url = "https://raw.githubusercontent.com/SimplifyJobs/New-Grad-Positions/refs/heads/dev/README.md"

    try:
        response = requests.get(url)
        response.raise_for_status()
        content = response.text

        # Parse the HTML table to extract job listings
        jobs = []

        # Find all table rows with job data
        # Look for <tr> tags that contain job information
        tr_pattern = r'<tr>\s*<td[^>]*><strong><a[^>]*>([^<]+)</a></strong></td>\s*<td[^>]*>([^<]+)</td>\s*<td[^>]*>([^<]+)</td>\s*<td[^>]*>.*?href="([^"]*)"[^>]*>.*?</td>'

        matches = re.findall(tr_pattern, content, re.DOTALL)

        for match in matches:
            company = match[0].strip()
            role = match[1].strip()
            location = match[2].strip()
            application_url = match[3].strip()

            # Clean up the data
            company = re.sub(r'\s+', ' ', company)
            role = re.sub(r'\s+', ' ', role)
            location = re.sub(r'\s+', ' ', location)

            jobs.append(JobListing(
                company=company,
                role=role,
                location=location,
                application_url=application_url
            ))

            if len(jobs) >= 10:  # Get top 10 instead of 5 for better results
                break

        return jobs
    except Exception as e:
        print(f"Error fetching jobs: {e}")
        return []


dataset_agent = Agent(
    name="DatasetAgent",
    instructions=DATASET_PROMPT,
    output_type=DatasetSummary,
)
